<?php
session_start();
require 'db_connect.php';

// Check if user is logged in
if (!isset($_SESSION['user_id'])) {
    header("Location: login.php");
    exit();
}

$error = '';
$success = '';
$user_data = null;

// Get current user data
try {
    $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
    $stmt->execute([$_SESSION['user_id']]);
    $user_data = $stmt->fetch();
    
    if (!$user_data) {
        session_destroy();
        header("Location: login.php");
        exit();
    }
} catch (PDOException $e) {
    $error = "Error fetching user data: " . $e->getMessage();
}

// Handle profile update
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['update_profile'])) {
    $new_username = trim($_POST['username']);
    $new_email = trim($_POST['email']);
    
    if (empty($new_username) || empty($new_email)) {
        $error = "Username and email are required!";
    } else {
        try {
            // VULNERABLE CODE: Second-order SQL injection
            // The username from the database (which may contain malicious SQL) 
            // is used directly in the query without proper escaping
            $current_username = $user_data['username']; // This may contain malicious SQL
            
            // This is the vulnerable query - it uses the stored username directly
            $vulnerable_query = "UPDATE users SET username = '$new_username', email = '$new_email' WHERE username = '$current_username' AND id = " . $_SESSION['user_id'];
            
            // Execute the vulnerable query
            $result = $conn->exec($vulnerable_query);
            
            if ($result !== false) {
                $success = "Profile updated successfully!";
                $_SESSION['username'] = $new_username;
                $_SESSION['email'] = $new_email;
                
                // Refresh user data
                $stmt = $conn->prepare("SELECT * FROM users WHERE id = ?");
                $stmt->execute([$_SESSION['user_id']]);
                $user_data = $stmt->fetch();
            } else {
                $error = "Failed to update profile!";
            }
            
        } catch (PDOException $e) {
            $error = "Update failed: " . $e->getMessage();
            
            // Log the vulnerable query for educational purposes
            error_log("Vulnerable query executed: " . $vulnerable_query);
        }
    }
}

// Handle password change
if ($_SERVER['REQUEST_METHOD'] == 'POST' && isset($_POST['change_password'])) {
    $current_password = $_POST['current_password'];
    $new_password = $_POST['new_password'];
    $confirm_password = $_POST['confirm_password'];
    
    if (empty($current_password) || empty($new_password) || empty($confirm_password)) {
        $error = "All password fields are required!";
    } elseif ($new_password !== $confirm_password) {
        $error = "New passwords do not match!";
    } elseif (strlen($new_password) < 6) {
        $error = "New password must be at least 6 characters long!";
    } else {
        // Verify current password
        if (password_verify($current_password, $user_data['password'])) {
            try {
                $hashed_password = password_hash($new_password, PASSWORD_DEFAULT);
                $stmt = $conn->prepare("UPDATE users SET password = ? WHERE id = ?");
                $stmt->execute([$hashed_password, $_SESSION['user_id']]);
                $success = "Password changed successfully!";
            } catch (PDOException $e) {
                $error = "Password change failed: " . $e->getMessage();
            }
        } else {
            $error = "Current password is incorrect!";
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Profile - SQL Injection Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <!-- Navigation -->
        <nav class="navbar navbar-expand-lg navbar-light mb-4">
            <div class="container-fluid">
                <span class="navbar-brand">
                    <i class="fas fa-user"></i> Welcome, <?php echo htmlspecialchars($_SESSION['username']); ?>
                </span>
                <div class="navbar-nav ms-auto">
                    <a href="index.php" class="nav-link">
                        <i class="fas fa-home"></i> Home
                    </a>
                    <a href="logout.php" class="nav-link">
                        <i class="fas fa-sign-out-alt"></i> Logout
                    </a>
                </div>
            </div>
        </nav>

        <?php if ($error): ?>
            <div class="alert alert-danger">
                <i class="fas fa-exclamation-circle"></i> <?php echo htmlspecialchars($error); ?>
            </div>
        <?php endif; ?>

        <?php if ($success): ?>
            <div class="alert alert-success">
                <i class="fas fa-check-circle"></i> <?php echo htmlspecialchars($success); ?>
            </div>
        <?php endif; ?>

        <div class="row">
            <!-- Profile Information -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-user-edit"></i> Update Profile</h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-danger">
                            <i class="fas fa-bug"></i>
                            <strong>VULNERABLE SECTION:</strong> This form contains a second-order SQL injection vulnerability!
                        </div>

                        <?php if ($user_data): ?>
                        <form method="POST" action="">
                            <div class="mb-3">
                                <label for="username" class="form-label">
                                    <i class="fas fa-user"></i> Username
                                </label>
                                <input type="text" class="form-control" id="username" name="username" 
                                       value="<?php echo htmlspecialchars($user_data['username']); ?>" required>
                                <small class="form-text text-danger">
                                    <i class="fas fa-exclamation-triangle"></i> 
                                    Current stored username may contain malicious SQL that will execute on update!
                                </small>
                            </div>

                            <div class="mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope"></i> Email
                                </label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo htmlspecialchars($user_data['email']); ?>" required>
                            </div>

                            <button type="submit" name="update_profile" class="btn btn-primary">
                                <i class="fas fa-save"></i> Update Profile
                            </button>
                        </form>
                        <?php endif; ?>
                    </div>
                </div>
            </div>

            <!-- Password Change -->
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-key"></i> Change Password</h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-success">
                            <i class="fas fa-shield-alt"></i>
                            <strong>SECURE SECTION:</strong> Password change uses prepared statements.
                        </div>

                        <form method="POST" action="">
                            <div class="mb-3">
                                <label for="current_password" class="form-label">Current Password</label>
                                <input type="password" class="form-control" id="current_password" name="current_password" required>
                            </div>

                            <div class="mb-3">
                                <label for="new_password" class="form-label">New Password</label>
                                <input type="password" class="form-control" id="new_password" name="new_password" minlength="6" required>
                            </div>

                            <div class="mb-3">
                                <label for="confirm_password" class="form-label">Confirm New Password</label>
                                <input type="password" class="form-control" id="confirm_password" name="confirm_password" minlength="6" required>
                            </div>

                            <button type="submit" name="change_password" class="btn btn-secondary">
                                <i class="fas fa-key"></i> Change Password
                            </button>
                        </form>
                    </div>
                </div>
            </div>
        </div>

        <!-- Current Profile Data -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-info-circle"></i> Current Profile Information</h3>
                    </div>
                    <div class="card-body">
                        <?php if ($user_data): ?>
                        <div class="profile-info">
                            <div class="row">
                                <div class="col-md-3"><strong>User ID:</strong></div>
                                <div class="col-md-9"><?php echo htmlspecialchars($user_data['id']); ?></div>
                            </div>
                            <div class="row">
                                <div class="col-md-3"><strong>Username:</strong></div>
                                <div class="col-md-9">
                                    <code><?php echo htmlspecialchars($user_data['username']); ?></code>
                                    <?php if (strpos($user_data['username'], "'") !== false || strpos($user_data['username'], ";") !== false): ?>
                                        <span class="badge bg-danger ms-2">
                                            <i class="fas fa-exclamation-triangle"></i> Contains SQL characters!
                                        </span>
                                    <?php endif; ?>
                                </div>
                            </div>
                            <div class="row">
                                <div class="col-md-3"><strong>Email:</strong></div>
                                <div class="col-md-9"><?php echo htmlspecialchars($user_data['email']); ?></div>
                            </div>
                            <div class="row">
                                <div class="col-md-3"><strong>Created:</strong></div>
                                <div class="col-md-9"><?php echo htmlspecialchars($user_data['created_at']); ?></div>
                            </div>
                            <div class="row">
                                <div class="col-md-3"><strong>Last Updated:</strong></div>
                                <div class="col-md-9"><?php echo htmlspecialchars($user_data['updated_at']); ?></div>
                            </div>
                        </div>
                        <?php endif; ?>
                    </div>
                </div>
            </div>
        </div>

        <!-- Vulnerability Explanation -->
        <div class="row mt-4">
            <div class="col-12">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-code"></i> Vulnerability Explanation</h3>
                    </div>
                    <div class="card-body">
                        <h5><i class="fas fa-bug"></i> Second-Order SQL Injection</h5>
                        <p>This page demonstrates a second-order SQL injection vulnerability:</p>
                        
                        <ol>
                            <li><strong>First Order:</strong> Malicious SQL is stored in the database (during registration)</li>
                            <li><strong>Second Order:</strong> The stored malicious SQL is executed when used in another query (profile update)</li>
                        </ol>

                        <h6>Vulnerable Code Pattern:</h6>
                        <pre class="bg-light p-3"><code>$current_username = $user_data['username']; // May contain malicious SQL
$query = "UPDATE users SET ... WHERE username = '$current_username'"; // VULNERABLE!</code></pre>

                        <h6>Secure Alternative:</h6>
                        <pre class="bg-light p-3"><code>$stmt = $conn->prepare("UPDATE users SET username = ?, email = ? WHERE id = ?");
$stmt->execute([$new_username, $new_email, $user_id]); // SECURE!</code></pre>

                        <div class="alert alert-warning mt-3">
                            <i class="fas fa-graduation-cap"></i>
                            <strong>Learning Objective:</strong> Always use prepared statements, even when data comes from your own database!
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
