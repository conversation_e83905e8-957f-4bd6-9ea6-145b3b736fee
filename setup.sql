-- SQL Injection Demo Database Schema
-- WARNING: This is for educational purposes only

CREATE DATABASE IF NOT EXISTS sql_injection_demo;
USE sql_injection_demo;

-- Users table
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

-- Sample data for testing
INSERT INTO users (username, email, password) VALUES 
('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'), -- password: password
('testuser', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'); -- password: password

-- Create a user for the application (adjust as needed)
-- GRANT ALL PRIVILEGES ON sql_injection_demo.* TO 'webapp'@'localhost' IDENTIFIED BY 'webapp_password';
-- FLUSH PRIVILEGES;
