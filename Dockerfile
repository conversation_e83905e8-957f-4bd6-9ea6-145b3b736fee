# SQL Injection Demo - Dockerfile
# WARNING: This container contains intentional security vulnerabilities for educational purposes only!

FROM php:8.1-apache

# Set working directory
WORKDIR /var/www/html

# Install system dependencies
RUN apt-get update && apt-get install -y \
    default-mysql-client \
    libzip-dev \
    zip \
    unzip \
    && docker-php-ext-install pdo pdo_mysql mysqli \
    && apt-get clean \
    && rm -rf /var/lib/apt/lists/*

# Enable Apache mod_rewrite
RUN a2enmod rewrite

# Copy application files
COPY . /var/www/html/

# Set proper permissions
RUN chown -R www-data:www-data /var/www/html \
    && chmod -R 755 /var/www/html

# Create a startup script
RUN echo '#!/bin/bash\n\
echo "🚀 Starting SQL Injection Demo Application..."\n\
echo "⚠️  WARNING: This application contains intentional vulnerabilities!"\n\
echo "📍 Application URL: http://localhost:8080"\n\
echo "🗄️  phpMyAdmin URL: http://localhost:8081"\n\
echo "🔑 Test Credentials: <EMAIL> / password"\n\
echo ""\n\
# Wait for MySQL to be ready\n\
echo "⏳ Waiting for MySQL to be ready..."\n\
while ! mysqladmin ping -h mysql -u root -proot_password --silent; do\n\
    sleep 1\n\
done\n\
echo "✅ MySQL is ready!"\n\
\n\
# Setup database\n\
echo "🗄️  Setting up database..."\n\
mysql -h mysql -u root -proot_password -e "CREATE DATABASE IF NOT EXISTS sql_injection_demo;"\n\
mysql -h mysql -u root -proot_password sql_injection_demo < /var/www/html/setup.sql\n\
echo "✅ Database setup complete!"\n\
\n\
echo "🎉 Application is ready!"\n\
echo ""\n\
echo "📋 Quick Test Instructions:"\n\
echo "1. Go to http://localhost:8080"\n\
echo "2. Register with malicious username: hacker'"'"'); INSERT INTO users (username, email, password) VALUES ('"'"'injected'"'"', '"'"'<EMAIL>'"'"', '"'"'password'"'"'); --"\n\
echo "3. Login and update profile to trigger injection"\n\
echo ""\n\
\n\
# Start Apache\n\
apache2-foreground' > /usr/local/bin/start-app.sh \
    && chmod +x /usr/local/bin/start-app.sh

# Expose port 80
EXPOSE 80

# Health check
HEALTHCHECK --interval=30s --timeout=3s --start-period=5s --retries=3 \
    CMD curl -f http://localhost/ || exit 1

# Start the application
CMD ["/usr/local/bin/start-app.sh"]
