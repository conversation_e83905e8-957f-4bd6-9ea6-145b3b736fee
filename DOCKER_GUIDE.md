# Docker Deployment Guide - SQL Injection Demo

**⚠️ WARNING: This Docker setup contains intentional security vulnerabilities for educational purposes only!**

## Overview

This Docker setup provides a complete, isolated environment for the SQL injection demonstration application with:
- **Web Server**: Apache with PHP 8.1
- **Database**: MySQL 8.0 with pre-configured vulnerable data
- **Database Management**: phpMyAdmin for easy database inspection
- **Automatic Setup**: Everything configured and ready to use

## Quick Start

### Prerequisites
- Docker Desktop (Windows/macOS) or Docker Engine (Linux)
- Docker Compose
- 4GB+ available RAM
- Ports 8080, 8081, and 3306 available

### One-Command Deployment
```bash
# Clone or download the project, then:
docker-compose up -d
```

### Using Management Scripts

**Linux/macOS:**
```bash
./docker-setup.sh start
```

**Windows:**
```batch
docker-setup.bat start
```

## Access Information

Once deployed, access the application at:

| Service | URL | Purpose |
|---------|-----|---------|
| **Web Application** | http://localhost:8080 | Main vulnerable application |
| **phpMyAdmin** | http://localhost:8081 | Database management interface |
| **MySQL Direct** | localhost:3306 | Direct database connection |

### Default Credentials

**Application Login:**
- Email: `<EMAIL>`
- Password: `password`

**Database Access:**
- Host: `localhost` (or `mysql` from within containers)
- Username: `root`
- Password: `root_password`
- Database: `sql_injection_demo`

## Container Architecture

### Services Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Web Server    │    │     MySQL       │    │   phpMyAdmin    │
│  (Apache+PHP)   │◄──►│   Database      │◄──►│   Management    │
│   Port: 8080    │    │   Port: 3306    │    │   Port: 8081    │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

### Container Details

**Web Container (`sql_injection_demo_web`):**
- Base: `php:8.1-apache`
- Includes: PDO MySQL extensions, curl
- Volume: Application code mounted for development
- Environment: Database connection variables

**MySQL Container (`sql_injection_demo_mysql`):**
- Base: `mysql:8.0`
- Database: Pre-configured with vulnerable schema
- Volume: Persistent data storage
- Init: Automatic schema and sample data loading

**phpMyAdmin Container (`sql_injection_demo_phpmyadmin`):**
- Base: `phpmyadmin/phpmyadmin:latest`
- Purpose: Web-based database management
- Access: Connected to MySQL container

## Management Commands

### Using Docker Compose Directly

```bash
# Start all services
docker-compose up -d

# Stop all services
docker-compose down

# View logs
docker-compose logs -f

# Restart specific service
docker-compose restart web

# Execute commands in containers
docker-compose exec web bash
docker-compose exec mysql mysql -u root -p

# Remove everything including volumes
docker-compose down -v --remove-orphans
```

### Using Management Scripts

**Available Commands:**
```bash
./docker-setup.sh start      # Start application
./docker-setup.sh stop       # Stop application
./docker-setup.sh restart    # Restart application
./docker-setup.sh logs       # View logs
./docker-setup.sh status     # Show container status
./docker-setup.sh reset-db   # Reset database to initial state
./docker-setup.sh cleanup    # Complete cleanup
./docker-setup.sh help       # Show help
```

## Development and Testing

### File Structure in Container

```
/var/www/html/
├── index.php              # Landing page
├── register.php           # User registration
├── login.php              # Authentication
├── profile.php            # VULNERABLE - Profile update
├── logout.php             # Session termination
├── db_connect.php         # Database configuration
├── css/style.css          # Styling
└── setup.sql              # Database schema
```

### Live Development

The application code is mounted as a volume, so changes to PHP files are immediately reflected:

```bash
# Edit files locally
nano profile.php

# Changes are immediately available at http://localhost:8080
```

### Database Inspection

**Via phpMyAdmin:**
1. Go to http://localhost:8081
2. Login with root/root_password
3. Select `sql_injection_demo` database
4. Browse tables and data

**Via Command Line:**
```bash
# Connect to MySQL container
docker-compose exec mysql mysql -u root -proot_password sql_injection_demo

# Run queries
mysql> SELECT * FROM users;
mysql> SHOW TABLES;
```

## Security Considerations

### Intentional Vulnerabilities

This setup contains **intentional security vulnerabilities**:
- Second-order SQL injection in profile updates
- Weak database passwords
- No input sanitization in vulnerable sections
- Debug information exposure

### Isolation Measures

**Network Isolation:**
- Custom Docker network isolates containers
- No external network access required
- Database not exposed to host by default

**Container Security:**
- Non-root user for web server processes
- Read-only filesystem where possible
- Minimal attack surface

### Safe Usage Guidelines

1. **Never expose to internet**
2. **Use only in isolated environments**
3. **Regular cleanup of containers and data**
4. **Monitor resource usage**
5. **Document all testing activities**

## Troubleshooting

### Common Issues

**Port Conflicts:**
```bash
# Check what's using ports
netstat -tulpn | grep :8080
netstat -tulpn | grep :3306

# Change ports in docker-compose.yml if needed
```

**Container Won't Start:**
```bash
# Check logs
docker-compose logs web
docker-compose logs mysql

# Check Docker daemon
docker info
```

**Database Connection Issues:**
```bash
# Test MySQL connectivity
docker-compose exec web ping mysql

# Check environment variables
docker-compose exec web env | grep DB_
```

**Permission Issues:**
```bash
# Fix file permissions
sudo chown -R $USER:$USER .
chmod +x docker-setup.sh
```

### Performance Optimization

**Resource Limits:**
```yaml
# Add to docker-compose.yml services
deploy:
  resources:
    limits:
      memory: 512M
      cpus: '0.5'
```

**Volume Optimization:**
```bash
# Use named volumes for better performance
docker volume create mysql_data
```

## Advanced Configuration

### Custom Database Configuration

Edit `docker-compose.yml` to modify MySQL settings:
```yaml
mysql:
  command: --default-authentication-plugin=mysql_native_password --innodb-buffer-pool-size=128M
```

### SSL/TLS Setup

For educational HTTPS testing:
```yaml
web:
  volumes:
    - ./ssl:/etc/ssl/certs
  environment:
    - APACHE_SSL_ENABLE=true
```

### Logging Configuration

Enhanced logging setup:
```yaml
web:
  logging:
    driver: "json-file"
    options:
      max-size: "10m"
      max-file: "3"
```

## Cleanup and Maintenance

### Regular Cleanup
```bash
# Stop and remove containers
docker-compose down

# Remove unused images
docker image prune -f

# Remove unused volumes
docker volume prune -f
```

### Complete Reset
```bash
# Nuclear option - removes everything
./docker-setup.sh cleanup
docker system prune -a -f --volumes
```

### Backup and Restore

**Backup Database:**
```bash
docker-compose exec mysql mysqldump -u root -proot_password sql_injection_demo > backup.sql
```

**Restore Database:**
```bash
docker-compose exec -T mysql mysql -u root -proot_password sql_injection_demo < backup.sql
```

## Educational Use

### Classroom Deployment

For educational environments:
1. Deploy on isolated network
2. Provide students with access URLs
3. Monitor resource usage
4. Regular cleanup between sessions

### Research Applications

For security research:
1. Document all test scenarios
2. Capture network traffic
3. Log all database interactions
4. Maintain test data integrity

---

**Remember**: This Docker setup is designed for educational purposes only. The intentional vulnerabilities make it unsuitable for any production use.
