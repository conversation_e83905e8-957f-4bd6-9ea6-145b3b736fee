# Docker ignore file for SQL Injection Demo

# Git files
.git
.gitignore

# Documentation (will be copied separately if needed)
*.md

# IDE files
.vscode/
.idea/
*.swp
*.swo

# OS files
.DS_Store
Thumbs.db

# Logs
*.log
logs/

# Temporary files
*.tmp
*.temp

# Node modules (if any)
node_modules/

# Backup files
*.bak
*.backup
backup_*.sql

# Setup scripts (not needed in container)
setup.sh
setup.bat

# Docker files (avoid recursion)
Dockerfile
docker-compose.yml
.dockerignore
