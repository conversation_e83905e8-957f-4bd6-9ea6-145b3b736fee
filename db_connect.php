<?php
/**
 * Database Connection Configuration
 * WARNING: This is for educational purposes only
 */

// Database configuration
$host = 'localhost';
$dbname = 'sql_injection_demo';
$username = 'root';  // Change this to your MySQL username
$password = '';      // Change this to your MySQL password

try {
    // Create PDO connection
    $conn = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    
    // Set PDO attributes
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    $conn->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);
    
} catch (PDOException $e) {
    // In a real application, you wouldn't expose database errors
    die("Connection failed: " . $e->getMessage() . "<br><br>
         <strong>Setup Instructions:</strong><br>
         1. Make sure MySQL is running<br>
         2. Create the database by running: <code>mysql -u root -p < setup.sql</code><br>
         3. Update the database credentials in db_connect.php<br>
         4. Ensure the database user has proper permissions");
}

// Function to get a safe database connection (for demonstration of proper practices)
function getSafeConnection() {
    global $host, $dbname, $username, $password;
    
    try {
        $safeConn = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
        $safeConn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $safeConn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        $safeConn->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);
        return $safeConn;
    } catch (PDOException $e) {
        return null;
    }
}
?>
