<?php
/**
 * Database Connection Configuration
 * WARNING: This is for educational purposes only
 */

// Database configuration - supports both Docker and local setup
$host = isset($_ENV['DB_HOST']) ? $_ENV['DB_HOST'] : (getenv('DB_HOST') ?: 'localhost');
$dbname = isset($_ENV['DB_NAME']) ? $_ENV['DB_NAME'] : (getenv('DB_NAME') ?: 'sql_injection_demo');
$username = isset($_ENV['DB_USER']) ? $_ENV['DB_USER'] : (getenv('DB_USER') ?: 'root');
$password = isset($_ENV['DB_PASS']) ? $_ENV['DB_PASS'] : (getenv('DB_PASS') ?: '');

// Docker environment detection
if (getenv('DB_HOST') === 'mysql') {
    $host = 'mysql';
    $password = 'root_password';
}

try {
    // Create PDO connection
    $conn = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
    
    // Set PDO attributes
    $conn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
    $conn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
    $conn->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);
    
} catch (PDOException $e) {
    // In a real application, you wouldn't expose database errors
    die("Connection failed: " . $e->getMessage() . "<br><br>
         <strong>Setup Instructions:</strong><br>
         1. Make sure MySQL is running<br>
         2. Create the database by running: <code>mysql -u root -p < setup.sql</code><br>
         3. Update the database credentials in db_connect.php<br>
         4. Ensure the database user has proper permissions");
}

// Function to get a safe database connection (for demonstration of proper practices)
function getSafeConnection() {
    global $host, $dbname, $username, $password;
    
    try {
        $safeConn = new PDO("mysql:host=$host;dbname=$dbname;charset=utf8mb4", $username, $password);
        $safeConn->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        $safeConn->setAttribute(PDO::ATTR_DEFAULT_FETCH_MODE, PDO::FETCH_ASSOC);
        $safeConn->setAttribute(PDO::ATTR_EMULATE_PREPARES, false);
        return $safeConn;
    } catch (PDOException $e) {
        return null;
    }
}
?>
