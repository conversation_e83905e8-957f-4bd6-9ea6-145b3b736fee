<?php
session_start();

// Destroy all session data
session_unset();
session_destroy();

// Clear session cookie
if (ini_get("session.use_cookies")) {
    $params = session_get_cookie_params();
    setcookie(session_name(), '', time() - 42000,
        $params["path"], $params["domain"],
        $params["secure"], $params["httponly"]
    );
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Logout - SQL Injection Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-sign-out-alt"></i> Logged Out Successfully</h3>
                    </div>
                    <div class="card-body text-center">
                        <div class="alert alert-success">
                            <i class="fas fa-check-circle"></i>
                            You have been successfully logged out of the SQL Injection Demo application.
                        </div>

                        <p>Thank you for using the educational demonstration!</p>

                        <div class="d-grid gap-2">
                            <a href="index.php" class="btn btn-primary">
                                <i class="fas fa-home"></i> Return to Home
                            </a>
                            <a href="login.php" class="btn btn-secondary">
                                <i class="fas fa-sign-in-alt"></i> Login Again
                            </a>
                        </div>
                    </div>
                </div>

                <!-- Security Note -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-shield-alt"></i> Security Note</h5>
                    </div>
                    <div class="card-body">
                        <small class="text-muted">
                            <i class="fas fa-info-circle"></i>
                            <strong>Secure Logout Implementation:</strong><br>
                            This logout functionality properly:
                            <ul class="mt-2">
                                <li>Destroys the server-side session</li>
                                <li>Clears session variables</li>
                                <li>Removes session cookies</li>
                                <li>Prevents session fixation attacks</li>
                            </ul>
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
    
    <!-- Auto-redirect after 5 seconds -->
    <script>
        setTimeout(function() {
            window.location.href = 'index.php';
        }, 5000);
    </script>
</body>
</html>
