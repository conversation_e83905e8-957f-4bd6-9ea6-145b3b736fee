# SQL Injection Demonstration Application

**⚠️ WARNING: This application contains intentional security vulnerabilities for educational purposes only. Do NOT deploy this in a production environment.**

## Overview

This web application demonstrates a second-order SQL injection vulnerability. It's designed for:
- Security research and education
- Penetration testing training
- Academic coursework on web security

## Features

- User registration and authentication
- Profile management
- **Intentional second-order SQL injection vulnerability** in profile update functionality
- Bootstrap-based responsive UI
- MySQL database backend

## Vulnerability Description

The application contains a second-order SQL injection vulnerability in the profile update functionality (`profile.php`). When a user registers with a malicious username containing SQL injection payloads, the payload is stored in the database. Later, when the user updates their profile, the stored malicious username is used in an unsafe SQL query, executing the injected SQL code.

### Example Attack Vector

1. Register with username: `admin'); DROP TABLE users; --`
2. <PERSON>gin with the registered credentials
3. Update profile information
4. The malicious SQL executes, potentially dropping the users table

## Setup Instructions

### Prerequisites

- Docker and Docker Compose
- Internet connection (for Bootstrap CDN)

### Quick Setup with Docker (Recommended)

#### One-Command Setup
```bash
docker-compose up -d
```

#### Access the Application
- **URL**: http://localhost:8080
- **phpMyAdmin**: http://localhost:8081 (optional database management)

#### Manual Setup Options

##### Linux/macOS
```bash
chmod +x setup.sh
sudo ./setup.sh
```

##### Windows (XAMPP)
```batch
setup.bat
```

### Manual Setup

1. **Database Setup**:
   ```sql
   CREATE DATABASE sql_injection_demo;
   USE sql_injection_demo;
   SOURCE setup.sql;
   ```

2. **Configure Database Connection**:
   Edit `db_connect.php` with your database credentials.

3. **Deploy Files**:
   Copy all files to your web server directory (e.g., `/var/www/html/sql_injection_demo/`).

4. **Set Permissions**:
   ```bash
   chmod 755 -R /var/www/html/sql_injection_demo/
   chown www-data:www-data -R /var/www/html/sql_injection_demo/
   ```

## Usage

### Docker (Recommended)
1. Navigate to `http://localhost:8080`
2. Use phpMyAdmin at `http://localhost:8081` for database inspection
3. Register a new user account
4. Login with your credentials
5. Access the profile page to test the vulnerability

### Manual Setup
1. Navigate to `http://localhost/sql_injection_demo/`
2. Register a new user account
3. Login with your credentials
4. Access the profile page to test the vulnerability

## Testing the Vulnerability

### Safe Test
1. Register with username: `testuser`
2. Login and update profile normally

### Malicious Test (Educational Only)
1. Register with username: `hacker'); INSERT INTO users (username, email, password) VALUES ('injected', '<EMAIL>', 'password'); --`
2. Login and update profile
3. Check if the injection executed by looking for the 'injected' user

## File Structure

```
sql_injection_demo/
├── index.php              # Landing page
├── register.php           # User registration
├── login.php              # User login
├── profile.php            # Profile management (VULNERABLE)
├── logout.php             # Logout functionality
├── db_connect.php         # Database connection
├── setup.sql              # Database schema
├── setup.sh               # Linux/macOS setup script
├── setup.bat              # Windows setup script
├── docker-setup.sh        # Docker management script (Linux/macOS)
├── docker-setup.bat       # Docker management script (Windows)
├── Dockerfile             # Docker container configuration
├── docker-compose.yml     # Docker services configuration
├── .dockerignore          # Docker ignore file
├── css/
│   └── style.css          # Custom styling
├── README.md              # This file
├── QUICK_START.md         # Quick start guide
├── DOCKER_GUIDE.md        # Comprehensive Docker guide
└── VULNERABILITY_GUIDE.md # Detailed vulnerability testing guide
```

## Security Notes

- This application is intentionally vulnerable
- Use only in isolated, controlled environments
- Never expose to the internet
- Always use prepared statements in production code
- This demonstrates why input validation and parameterized queries are essential

## Educational Use

This application is designed for:
- Cybersecurity courses
- Penetration testing training
- Security awareness demonstrations
- Research into SQL injection techniques

## Disclaimer

This software is provided for educational purposes only. The authors are not responsible for any misuse or damage caused by this application. Use responsibly and only in authorized environments.
