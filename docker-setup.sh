#!/bin/bash

# Docker Setup Script for SQL Injection Demo
# This script provides easy Docker management commands

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
COMPOSE_FILE="docker-compose.yml"
PROJECT_NAME="sql-injection-demo"

echo -e "${RED}⚠️  WARNING: This sets up an intentionally vulnerable web application!${NC}"
echo -e "${RED}   Use only in controlled environments for educational purposes.${NC}"
echo ""

# Function to check if Docker is installed
check_docker() {
    if ! command -v docker &> /dev/null; then
        echo -e "${RED}❌ Docker is not installed!${NC}"
        echo "Please install Docker first: https://docs.docker.com/get-docker/"
        exit 1
    fi

    if ! command -v docker-compose &> /dev/null; then
        echo -e "${RED}❌ Docker Compose is not installed!${NC}"
        echo "Please install Docker Compose first: https://docs.docker.com/compose/install/"
        exit 1
    fi

    echo -e "${GREEN}✅ Docker and Docker Compose are installed${NC}"
}

# Function to start the application
start_app() {
    echo -e "${BLUE}🚀 Starting SQL Injection Demo...${NC}"
    
    # Build and start containers
    docker-compose -p "$PROJECT_NAME" up -d --build
    
    echo -e "${YELLOW}⏳ Waiting for services to be ready...${NC}"
    sleep 10
    
    # Check if services are running
    if docker-compose -p "$PROJECT_NAME" ps | grep -q "Up"; then
        echo -e "${GREEN}🎉 Application is running!${NC}"
        echo ""
        echo -e "${BLUE}📍 Access Information:${NC}"
        echo "   🌐 Web Application: http://localhost:8080"
        echo "   🗄️  phpMyAdmin: http://localhost:8081"
        echo ""
        echo -e "${BLUE}🔑 Test Credentials:${NC}"
        echo "   📧 Email: <EMAIL>"
        echo "   🔒 Password: password"
        echo ""
        echo -e "${YELLOW}🧪 Quick Vulnerability Test:${NC}"
        echo "   1. Register with username: hacker'); INSERT INTO users (username, email, password) VALUES ('injected', '<EMAIL>', 'password'); --"
        echo "   2. Login and update profile to trigger injection"
        echo ""
        
        # Ask to open browser
        read -p "Open browser now? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            if command -v xdg-open &> /dev/null; then
                xdg-open "http://localhost:8080"
            elif command -v open &> /dev/null; then
                open "http://localhost:8080"
            else
                echo "Please open http://localhost:8080 in your browser"
            fi
        fi
    else
        echo -e "${RED}❌ Failed to start application${NC}"
        echo "Check logs with: docker-compose -p $PROJECT_NAME logs"
        exit 1
    fi
}

# Function to stop the application
stop_app() {
    echo -e "${YELLOW}🛑 Stopping SQL Injection Demo...${NC}"
    docker-compose -p "$PROJECT_NAME" down
    echo -e "${GREEN}✅ Application stopped${NC}"
}

# Function to restart the application
restart_app() {
    echo -e "${YELLOW}🔄 Restarting SQL Injection Demo...${NC}"
    docker-compose -p "$PROJECT_NAME" restart
    echo -e "${GREEN}✅ Application restarted${NC}"
}

# Function to view logs
view_logs() {
    echo -e "${BLUE}📋 Viewing application logs...${NC}"
    docker-compose -p "$PROJECT_NAME" logs -f
}

# Function to clean up everything
cleanup() {
    echo -e "${YELLOW}🧹 Cleaning up SQL Injection Demo...${NC}"
    docker-compose -p "$PROJECT_NAME" down -v --remove-orphans
    docker system prune -f
    echo -e "${GREEN}✅ Cleanup complete${NC}"
}

# Function to reset database
reset_db() {
    echo -e "${YELLOW}🔄 Resetting database...${NC}"
    docker-compose -p "$PROJECT_NAME" exec mysql mysql -u root -proot_password -e "DROP DATABASE IF EXISTS sql_injection_demo; CREATE DATABASE sql_injection_demo;"
    docker-compose -p "$PROJECT_NAME" exec mysql mysql -u root -proot_password sql_injection_demo < setup.sql
    echo -e "${GREEN}✅ Database reset complete${NC}"
}

# Function to show status
show_status() {
    echo -e "${BLUE}📊 Application Status:${NC}"
    docker-compose -p "$PROJECT_NAME" ps
}

# Function to show help
show_help() {
    echo -e "${BLUE}🔧 SQL Injection Demo - Docker Management${NC}"
    echo ""
    echo "Usage: $0 [COMMAND]"
    echo ""
    echo "Commands:"
    echo "  start     Start the application"
    echo "  stop      Stop the application"
    echo "  restart   Restart the application"
    echo "  logs      View application logs"
    echo "  status    Show container status"
    echo "  reset-db  Reset the database"
    echo "  cleanup   Remove all containers and volumes"
    echo "  help      Show this help message"
    echo ""
    echo "Examples:"
    echo "  $0 start"
    echo "  $0 logs"
    echo "  $0 cleanup"
}

# Main script logic
case "${1:-start}" in
    "start")
        check_docker
        start_app
        ;;
    "stop")
        stop_app
        ;;
    "restart")
        restart_app
        ;;
    "logs")
        view_logs
        ;;
    "status")
        show_status
        ;;
    "reset-db")
        reset_db
        ;;
    "cleanup")
        cleanup
        ;;
    "help"|"-h"|"--help")
        show_help
        ;;
    *)
        echo -e "${RED}❌ Unknown command: $1${NC}"
        show_help
        exit 1
        ;;
esac
