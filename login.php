<?php
session_start();
require 'db_connect.php';

// Redirect if already logged in
if (isset($_SESSION['user_id'])) {
    header("Location: profile.php");
    exit();
}

$error = '';

if ($_SERVER['REQUEST_METHOD'] == 'POST') {
    $email = trim($_POST['email']);
    $password = $_POST['password'];

    if (empty($email) || empty($password)) {
        $error = "Email and password are required!";
    } else {
        try {
            // Use prepared statement for login (secure)
            $stmt = $conn->prepare("SELECT id, username, email, password FROM users WHERE email = ?");
            $stmt->execute([$email]);
            $user = $stmt->fetch();

            if ($user && password_verify($password, $user['password'])) {
                // Login successful
                $_SESSION['user_id'] = $user['id'];
                $_SESSION['username'] = $user['username'];
                $_SESSION['email'] = $user['email'];
                
                header("Location: profile.php");
                exit();
            } else {
                $error = "Invalid email or password!";
            }
        } catch (PDOException $e) {
            $error = "Login failed: " . $e->getMessage();
        }
    }
}
?>

<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Login - SQL Injection Demo</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <!-- Navigation -->
        <div class="text-center mb-4">
            <a href="index.php" class="btn btn-outline-light">
                <i class="fas fa-arrow-left"></i> Back to Home
            </a>
        </div>

        <div class="row justify-content-center">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-sign-in-alt"></i> Login to Your Account</h3>
                    </div>
                    <div class="card-body">
                        <?php if ($error): ?>
                            <div class="alert alert-danger">
                                <i class="fas fa-exclamation-circle"></i> <?php echo htmlspecialchars($error); ?>
                            </div>
                        <?php endif; ?>

                        <form method="POST" action="">
                            <div class="mb-3">
                                <label for="email" class="form-label">
                                    <i class="fas fa-envelope"></i> Email Address
                                </label>
                                <input type="email" class="form-control" id="email" name="email" 
                                       value="<?php echo isset($_POST['email']) ? htmlspecialchars($_POST['email']) : ''; ?>" 
                                       required>
                            </div>

                            <div class="mb-3">
                                <label for="password" class="form-label">
                                    <i class="fas fa-lock"></i> Password
                                </label>
                                <input type="password" class="form-control" id="password" name="password" required>
                            </div>

                            <div class="d-grid gap-2">
                                <button type="submit" class="btn btn-primary">
                                    <i class="fas fa-sign-in-alt"></i> Login
                                </button>
                            </div>
                        </form>

                        <div class="text-center mt-3">
                            <small>
                                Don't have an account? 
                                <a href="register.php" class="text-decoration-none">
                                    <i class="fas fa-user-plus"></i> Register here
                                </a>
                            </small>
                        </div>
                    </div>
                </div>

                <!-- Test Credentials -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-key"></i> Test Credentials</h5>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h6>Admin Account:</h6>
                                <small class="text-muted">
                                    <strong>Email:</strong> <EMAIL><br>
                                    <strong>Password:</strong> password
                                </small>
                            </div>
                            <div class="col-md-6">
                                <h6>Test User:</h6>
                                <small class="text-muted">
                                    <strong>Email:</strong> <EMAIL><br>
                                    <strong>Password:</strong> password
                                </small>
                            </div>
                        </div>
                        
                        <div class="alert alert-info mt-3">
                            <i class="fas fa-info-circle"></i>
                            <strong>Note:</strong> Login functionality uses secure prepared statements. 
                            The vulnerability is in the profile update feature.
                        </div>
                    </div>
                </div>

                <!-- Security Information -->
                <div class="card mt-4">
                    <div class="card-header">
                        <h5><i class="fas fa-shield-alt"></i> Security Information</h5>
                    </div>
                    <div class="card-body">
                        <h6><i class="fas fa-check-circle text-success"></i> Secure Components:</h6>
                        <ul class="small">
                            <li>Login authentication uses prepared statements</li>
                            <li>Passwords are properly hashed with PHP's password_hash()</li>
                            <li>Session management is implemented</li>
                            <li>Input validation on login form</li>
                        </ul>

                        <h6><i class="fas fa-exclamation-triangle text-warning"></i> Vulnerable Components:</h6>
                        <ul class="small">
                            <li>Profile update functionality (second-order SQL injection)</li>
                            <li>Username field stored without proper sanitization</li>
                            <li>Dynamic query construction in profile updates</li>
                        </ul>

                        <div class="alert alert-warning mt-2">
                            <small>
                                <i class="fas fa-graduation-cap"></i>
                                <strong>Educational Purpose:</strong> This demonstrates the importance of 
                                consistent security practices throughout an application.
                            </small>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
