@echo off
REM Docker Setup Script for SQL Injection Demo (Windows)
REM This script provides easy Docker management commands

setlocal enabledelayedexpansion

set "PROJECT_NAME=sql-injection-demo"
set "COMPOSE_FILE=docker-compose.yml"

echo.
echo ===============================================================================
echo                    SQL INJECTION DEMO - DOCKER SETUP
echo ===============================================================================
echo.
echo WARNING: This sets up an intentionally vulnerable web application!
echo          Use only in controlled environments for educational purposes.
echo.

REM Function to check if Docker is installed
:check_docker
echo Checking Docker installation...
docker --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Docker is not installed!
    echo Please install Docker Desktop first: https://docs.docker.com/desktop/windows/
    pause
    exit /b 1
)

docker-compose --version >nul 2>&1
if errorlevel 1 (
    echo ERROR: Docker Compose is not installed!
    echo Please install Docker Compose or update Docker Desktop.
    pause
    exit /b 1
)

echo Docker and Docker Compose are installed.
echo.
goto :main

REM Function to start the application
:start_app
echo Starting SQL Injection Demo...
docker-compose -p %PROJECT_NAME% up -d --build

echo Waiting for services to be ready...
timeout /t 10 /nobreak >nul

REM Check if services are running
docker-compose -p %PROJECT_NAME% ps | find "Up" >nul
if errorlevel 1 (
    echo ERROR: Failed to start application
    echo Check logs with: docker-compose -p %PROJECT_NAME% logs
    pause
    exit /b 1
)

echo.
echo ===============================================================================
echo                           APPLICATION IS RUNNING!
echo ===============================================================================
echo.
echo Access Information:
echo   Web Application: http://localhost:8080
echo   phpMyAdmin: http://localhost:8081
echo.
echo Test Credentials:
echo   Email: <EMAIL>
echo   Password: password
echo.
echo Quick Vulnerability Test:
echo   1. Register with username: hacker'); INSERT INTO users (username, email, password) VALUES ('injected', '<EMAIL>', 'password'); --
echo   2. Login and update profile to trigger injection
echo.

set /p open_browser="Open browser now? (y/N): "
if /i "%open_browser%"=="y" (
    start http://localhost:8080
)
goto :eof

REM Function to stop the application
:stop_app
echo Stopping SQL Injection Demo...
docker-compose -p %PROJECT_NAME% down
echo Application stopped.
goto :eof

REM Function to restart the application
:restart_app
echo Restarting SQL Injection Demo...
docker-compose -p %PROJECT_NAME% restart
echo Application restarted.
goto :eof

REM Function to view logs
:view_logs
echo Viewing application logs...
docker-compose -p %PROJECT_NAME% logs -f
goto :eof

REM Function to clean up everything
:cleanup
echo Cleaning up SQL Injection Demo...
docker-compose -p %PROJECT_NAME% down -v --remove-orphans
docker system prune -f
echo Cleanup complete.
goto :eof

REM Function to reset database
:reset_db
echo Resetting database...
docker-compose -p %PROJECT_NAME% exec mysql mysql -u root -proot_password -e "DROP DATABASE IF EXISTS sql_injection_demo; CREATE DATABASE sql_injection_demo;"
docker-compose -p %PROJECT_NAME% exec mysql mysql -u root -proot_password sql_injection_demo < setup.sql
echo Database reset complete.
goto :eof

REM Function to show status
:show_status
echo Application Status:
docker-compose -p %PROJECT_NAME% ps
goto :eof

REM Function to show help
:show_help
echo.
echo SQL Injection Demo - Docker Management
echo.
echo Usage: %~nx0 [COMMAND]
echo.
echo Commands:
echo   start     Start the application
echo   stop      Stop the application
echo   restart   Restart the application
echo   logs      View application logs
echo   status    Show container status
echo   reset-db  Reset the database
echo   cleanup   Remove all containers and volumes
echo   help      Show this help message
echo.
echo Examples:
echo   %~nx0 start
echo   %~nx0 logs
echo   %~nx0 cleanup
echo.
goto :eof

REM Main script logic
:main
if "%1"=="" goto start_app
if /i "%1"=="start" goto start_app
if /i "%1"=="stop" goto stop_app
if /i "%1"=="restart" goto restart_app
if /i "%1"=="logs" goto view_logs
if /i "%1"=="status" goto show_status
if /i "%1"=="reset-db" goto reset_db
if /i "%1"=="cleanup" goto cleanup
if /i "%1"=="help" goto show_help
if /i "%1"=="-h" goto show_help
if /i "%1"=="--help" goto show_help

echo ERROR: Unknown command: %1
call :show_help
pause
exit /b 1

:start_app
call :check_docker
call :start_app
pause
goto :eof
