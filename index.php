<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SQL Injection Demo - Home</title>
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/css/bootstrap.min.css" rel="stylesheet">
    <link rel="stylesheet" href="css/style.css">
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
</head>
<body>
    <div class="container mt-5">
        <!-- Warning Banner -->
        <div class="vulnerability-warning">
            <i class="fas fa-exclamation-triangle"></i>
            <strong>WARNING:</strong> This application contains intentional security vulnerabilities for educational purposes only!
        </div>

        <!-- Welcome Section -->
        <div class="welcome-section">
            <h1><i class="fas fa-shield-alt"></i> SQL Injection Demonstration</h1>
            <p>Educational Web Application for Security Research</p>
        </div>

        <!-- Main Content -->
        <div class="row justify-content-center mt-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-home"></i> Welcome to the SQL Injection Demo</h3>
                    </div>
                    <div class="card-body">
                        <div class="row">
                            <div class="col-md-6">
                                <h5><i class="fas fa-info-circle"></i> About This Application</h5>
                                <p>This web application demonstrates a <strong>second-order SQL injection vulnerability</strong> for educational purposes.</p>
                                
                                <h6><i class="fas fa-bug"></i> Vulnerability Type:</h6>
                                <ul>
                                    <li>Second-order SQL injection in profile update</li>
                                    <li>Stored malicious payload execution</li>
                                    <li>Unsafe dynamic query construction</li>
                                </ul>

                                <h6><i class="fas fa-graduation-cap"></i> Educational Use:</h6>
                                <ul>
                                    <li>Security research and training</li>
                                    <li>Penetration testing practice</li>
                                    <li>Academic coursework</li>
                                </ul>
                            </div>
                            <div class="col-md-6">
                                <h5><i class="fas fa-play"></i> Get Started</h5>
                                <p>Choose an option below to begin:</p>
                                
                                <div class="d-grid gap-2">
                                    <a href="register.php" class="btn btn-primary">
                                        <i class="fas fa-user-plus"></i> Register New Account
                                    </a>
                                    <a href="login.php" class="btn btn-secondary">
                                        <i class="fas fa-sign-in-alt"></i> Login to Existing Account
                                    </a>
                                </div>

                                <div class="mt-4">
                                    <h6><i class="fas fa-vial"></i> Test Credentials:</h6>
                                    <small class="text-muted">
                                        <strong>Email:</strong> <EMAIL><br>
                                        <strong>Password:</strong> password
                                    </small>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Instructions Section -->
        <div class="row justify-content-center mt-4">
            <div class="col-md-8">
                <div class="card">
                    <div class="card-header">
                        <h3><i class="fas fa-list-ol"></i> How to Test the Vulnerability</h3>
                    </div>
                    <div class="card-body">
                        <div class="alert alert-warning">
                            <i class="fas fa-exclamation-triangle"></i>
                            <strong>Educational Purpose Only:</strong> Use only in controlled environments for learning.
                        </div>

                        <h5><i class="fas fa-shield-alt"></i> Safe Testing Steps:</h5>
                        <ol>
                            <li><strong>Register:</strong> Create a new account with normal credentials</li>
                            <li><strong>Login:</strong> Access your account</li>
                            <li><strong>Profile:</strong> Navigate to profile page and update information</li>
                            <li><strong>Observe:</strong> Normal behavior with safe inputs</li>
                        </ol>

                        <h5><i class="fas fa-bug"></i> Vulnerability Testing:</h5>
                        <ol>
                            <li><strong>Register with malicious username:</strong> 
                                <code>hacker'); INSERT INTO users (username, email, password) VALUES ('injected', '<EMAIL>', 'password'); --</code>
                            </li>
                            <li><strong>Login:</strong> Use the registered credentials</li>
                            <li><strong>Update Profile:</strong> Modify any profile information</li>
                            <li><strong>Check Results:</strong> Look for evidence of SQL injection execution</li>
                        </ol>

                        <div class="alert alert-danger mt-3">
                            <i class="fas fa-exclamation-circle"></i>
                            <strong>Important:</strong> This demonstrates why input validation and prepared statements are crucial for web security.
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Footer -->
        <div class="text-center mt-5 mb-3">
            <small class="text-light">
                <i class="fas fa-code"></i> Created for educational purposes | 
                <i class="fas fa-shield-alt"></i> Always use secure coding practices in production
            </small>
        </div>
    </div>

    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.0/dist/js/bootstrap.bundle.min.js"></script>
</body>
</html>
