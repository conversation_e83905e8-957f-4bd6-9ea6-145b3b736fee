#!/bin/bash

# SQL Injection Demo Setup Script for Linux/macOS
# WARNING: This sets up an intentionally vulnerable application for educational purposes only!

set -e

# Colors for output
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
NC='\033[0m' # No Color

# Configuration
WEB_DIR="/var/www/html/sql_injection_demo"
DB_NAME="sql_injection_demo"
DB_USER="root"
DB_PASS=""

echo -e "${RED}⚠️  WARNING: This script sets up an intentionally vulnerable web application!${NC}"
echo -e "${RED}   Use only in controlled environments for educational purposes.${NC}"
echo ""
read -p "Do you want to continue? (y/N): " -n 1 -r
echo
if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    echo "Setup cancelled."
    exit 1
fi

echo -e "${BLUE}🚀 Starting SQL Injection Demo Setup...${NC}"

# Function to check if command exists
command_exists() {
    command -v "$1" >/dev/null 2>&1
}

# Function to install packages on different systems
install_packages() {
    if command_exists apt-get; then
        # Ubuntu/Debian
        echo -e "${YELLOW}📦 Installing packages on Ubuntu/Debian...${NC}"
        sudo apt update
        sudo apt install -y apache2 php php-mysql libapache2-mod-php mysql-server
        sudo systemctl start apache2
        sudo systemctl enable apache2
        sudo systemctl start mysql
        sudo systemctl enable mysql
    elif command_exists yum; then
        # CentOS/RHEL
        echo -e "${YELLOW}📦 Installing packages on CentOS/RHEL...${NC}"
        sudo yum install -y httpd php php-mysql mariadb-server
        sudo systemctl start httpd
        sudo systemctl enable httpd
        sudo systemctl start mariadb
        sudo systemctl enable mariadb
        WEB_DIR="/var/www/html/sql_injection_demo"
    elif command_exists brew; then
        # macOS with Homebrew
        echo -e "${YELLOW}📦 Installing packages on macOS...${NC}"
        brew install php mysql
        brew services start mysql
        WEB_DIR="/usr/local/var/www/sql_injection_demo"
        mkdir -p "$WEB_DIR"
    else
        echo -e "${RED}❌ Unsupported system. Please install Apache, PHP, and MySQL manually.${NC}"
        exit 1
    fi
}

# Check if MySQL is accessible
check_mysql() {
    echo -e "${YELLOW}🔍 Checking MySQL connection...${NC}"
    if ! mysql -u"$DB_USER" -p"$DB_PASS" -e "SELECT 1;" >/dev/null 2>&1; then
        echo -e "${YELLOW}⚠️  MySQL connection failed. You may need to set a password.${NC}"
        read -p "Enter MySQL root password (or press Enter if none): " -s mysql_pass
        echo
        DB_PASS="$mysql_pass"
        
        if ! mysql -u"$DB_USER" -p"$DB_PASS" -e "SELECT 1;" >/dev/null 2>&1; then
            echo -e "${RED}❌ Still cannot connect to MySQL. Please check your MySQL installation.${NC}"
            exit 1
        fi
    fi
    echo -e "${GREEN}✅ MySQL connection successful${NC}"
}

# Setup database
setup_database() {
    echo -e "${YELLOW}🗄️  Setting up database...${NC}"
    
    # Create database
    mysql -u"$DB_USER" -p"$DB_PASS" -e "CREATE DATABASE IF NOT EXISTS $DB_NAME;"
    
    # Create table and insert sample data
    mysql -u"$DB_USER" -p"$DB_PASS" "$DB_NAME" << EOF
CREATE TABLE IF NOT EXISTS users (
    id INT AUTO_INCREMENT PRIMARY KEY,
    username VARCHAR(255) NOT NULL,
    email VARCHAR(255) NOT NULL UNIQUE,
    password VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP,
    updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP
);

INSERT IGNORE INTO users (username, email, password) VALUES 
('admin', '<EMAIL>', '\$2y\$10\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'),
('testuser', '<EMAIL>', '\$2y\$10\$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi');
EOF

    echo -e "${GREEN}✅ Database setup complete${NC}"
}

# Setup web directory
setup_web_directory() {
    echo -e "${YELLOW}📁 Setting up web directory...${NC}"
    
    # Create directory
    sudo mkdir -p "$WEB_DIR"
    sudo mkdir -p "$WEB_DIR/css"
    
    # Copy files
    sudo cp -r ./* "$WEB_DIR/" 2>/dev/null || true
    
    # Set permissions
    if command_exists apt-get; then
        sudo chown -R www-data:www-data "$WEB_DIR"
    elif command_exists yum; then
        sudo chown -R apache:apache "$WEB_DIR"
    else
        sudo chown -R $(whoami):$(whoami) "$WEB_DIR"
    fi
    
    sudo chmod -R 755 "$WEB_DIR"
    
    echo -e "${GREEN}✅ Web directory setup complete${NC}"
}

# Update database configuration
update_db_config() {
    echo -e "${YELLOW}⚙️  Updating database configuration...${NC}"
    
    # Update db_connect.php with the correct password
    sudo sed -i "s/\$password = '';/\$password = '$DB_PASS';/" "$WEB_DIR/db_connect.php"
    
    echo -e "${GREEN}✅ Database configuration updated${NC}"
}

# Main setup process
main() {
    echo -e "${BLUE}📋 Setup Process:${NC}"
    echo "1. Install required packages"
    echo "2. Check MySQL connection"
    echo "3. Setup database"
    echo "4. Setup web directory"
    echo "5. Update configuration"
    echo ""
    
    install_packages
    check_mysql
    setup_database
    setup_web_directory
    update_db_config
    
    echo ""
    echo -e "${GREEN}🎉 Setup Complete!${NC}"
    echo ""
    echo -e "${BLUE}📝 Access Information:${NC}"
    echo "   URL: http://localhost/sql_injection_demo/"
    echo "   Database: $DB_NAME"
    echo "   Web Directory: $WEB_DIR"
    echo ""
    echo -e "${BLUE}🔑 Test Credentials:${NC}"
    echo "   Email: <EMAIL>"
    echo "   Password: password"
    echo ""
    echo -e "${YELLOW}⚠️  Security Reminders:${NC}"
    echo "   • This application is intentionally vulnerable"
    echo "   • Use only in isolated environments"
    echo "   • Never expose to the internet"
    echo "   • For educational purposes only"
    echo ""
    echo -e "${BLUE}🧪 Testing the Vulnerability:${NC}"
    echo "   1. Register with username: hacker'); INSERT INTO users (username, email, password) VALUES ('injected', '<EMAIL>', 'password'); --"
    echo "   2. Login with the registered account"
    echo "   3. Update profile to trigger the injection"
    echo ""
    
    # Try to open browser
    if command_exists xdg-open; then
        read -p "Open browser now? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            xdg-open "http://localhost/sql_injection_demo/"
        fi
    elif command_exists open; then
        read -p "Open browser now? (y/N): " -n 1 -r
        echo
        if [[ $REPLY =~ ^[Yy]$ ]]; then
            open "http://localhost/sql_injection_demo/"
        fi
    fi
}

# Run main function
main
