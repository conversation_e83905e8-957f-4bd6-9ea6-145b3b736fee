# SQL Injection Demo - Docker Compose Configuration
# WARNING: This setup contains intentional security vulnerabilities for educational purposes only!

version: '3.8'

services:
  # Web Application (Apache + PHP)
  web:
    build: .
    container_name: sql_injection_demo_web
    ports:
      - "8080:80"
    depends_on:
      - mysql
    environment:
      - DB_HOST=mysql
      - DB_NAME=sql_injection_demo
      - DB_USER=root
      - DB_PASS=root_password
    volumes:
      - ./:/var/www/html
    networks:
      - sql_demo_network
    restart: unless-stopped
    labels:
      - "traefik.enable=false"
      - "description=SQL Injection Demo Web Application (VULNERABLE - Educational Use Only)"

  # MySQL Database
  mysql:
    image: mysql:8.0
    container_name: sql_injection_demo_mysql
    environment:
      MYSQL_ROOT_PASSWORD: root_password
      MYSQL_DATABASE: sql_injection_demo
      MYSQL_USER: webapp
      MYSQL_PASSWORD: webapp_password
    ports:
      - "3306:3306"
    volumes:
      - mysql_data:/var/lib/mysql
      - ./setup.sql:/docker-entrypoint-initdb.d/setup.sql
    networks:
      - sql_demo_network
    restart: unless-stopped
    command: --default-authentication-plugin=mysql_native_password
    labels:
      - "description=MySQL Database for SQL Injection Demo"

  # phpMyAdmin (Optional - for database management)
  phpmyadmin:
    image: phpmyadmin/phpmyadmin:latest
    container_name: sql_injection_demo_phpmyadmin
    environment:
      PMA_HOST: mysql
      PMA_USER: root
      PMA_PASSWORD: root_password
      MYSQL_ROOT_PASSWORD: root_password
    ports:
      - "8081:80"
    depends_on:
      - mysql
    networks:
      - sql_demo_network
    restart: unless-stopped
    labels:
      - "description=phpMyAdmin for SQL Injection Demo Database Management"

# Named volumes
volumes:
  mysql_data:
    driver: local
    labels:
      - "description=MySQL data volume for SQL Injection Demo"

# Custom network
networks:
  sql_demo_network:
    driver: bridge
    labels:
      - "description=Isolated network for SQL Injection Demo"
