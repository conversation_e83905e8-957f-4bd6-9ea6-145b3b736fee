@echo off
REM SQL Injection Demo Setup Script for Windows (XAMPP)
REM WARNING: This sets up an intentionally vulnerable application for educational purposes only!

setlocal enabledelayedexpansion

echo.
echo ===============================================================================
echo                    SQL INJECTION DEMO SETUP FOR WINDOWS
echo ===============================================================================
echo.
echo WARNING: This script sets up an intentionally vulnerable web application!
echo          Use only in controlled environments for educational purposes.
echo.
set /p continue="Do you want to continue? (y/N): "
if /i not "%continue%"=="y" (
    echo Setup cancelled.
    pause
    exit /b 1
)

echo.
echo Starting SQL Injection Demo Setup...
echo.

REM Configuration
set "XAMPP_DIR=C:\xampp"
set "WEB_DIR=%XAMPP_DIR%\htdocs\sql_injection_demo"
set "DB_NAME=sql_injection_demo"
set "MYSQL_BIN=%XAMPP_DIR%\mysql\bin\mysql.exe"

REM Check if XAMPP is installed
echo Checking for XAMPP installation...
if not exist "%XAMPP_DIR%" (
    echo.
    echo ERROR: XAMPP not found at %XAMPP_DIR%
    echo.
    echo Please install XAMPP first:
    echo 1. Download from: https://www.apachefriends.org/download.html
    echo 2. Install to C:\xampp
    echo 3. Run this script again
    echo.
    pause
    exit /b 1
)

echo XAMPP found at %XAMPP_DIR%
echo.

REM Check if XAMPP services are running
echo Checking XAMPP services...
tasklist /FI "IMAGENAME eq httpd.exe" 2>NUL | find /I /N "httpd.exe">NUL
if "%ERRORLEVEL%"=="1" (
    echo.
    echo WARNING: Apache is not running!
    echo Please start Apache in XAMPP Control Panel and try again.
    echo.
    pause
    exit /b 1
)

tasklist /FI "IMAGENAME eq mysqld.exe" 2>NUL | find /I /N "mysqld.exe">NUL
if "%ERRORLEVEL%"=="1" (
    echo.
    echo WARNING: MySQL is not running!
    echo Please start MySQL in XAMPP Control Panel and try again.
    echo.
    pause
    exit /b 1
)

echo Apache and MySQL are running.
echo.

REM Create web directory
echo Setting up web directory...
if not exist "%WEB_DIR%" mkdir "%WEB_DIR%"
if not exist "%WEB_DIR%\css" mkdir "%WEB_DIR%\css"

REM Copy files
echo Copying application files...
copy "*.php" "%WEB_DIR%\" >nul 2>&1
copy "*.sql" "%WEB_DIR%\" >nul 2>&1
copy "*.md" "%WEB_DIR%\" >nul 2>&1
copy "css\*" "%WEB_DIR%\css\" >nul 2>&1

echo Files copied to %WEB_DIR%
echo.

REM Setup database
echo Setting up database...
echo Creating database and tables...

REM Create SQL commands file
echo CREATE DATABASE IF NOT EXISTS %DB_NAME%; > temp_setup.sql
echo USE %DB_NAME%; >> temp_setup.sql
echo CREATE TABLE IF NOT EXISTS users ( >> temp_setup.sql
echo     id INT AUTO_INCREMENT PRIMARY KEY, >> temp_setup.sql
echo     username VARCHAR(255) NOT NULL, >> temp_setup.sql
echo     email VARCHAR(255) NOT NULL UNIQUE, >> temp_setup.sql
echo     password VARCHAR(255) NOT NULL, >> temp_setup.sql
echo     created_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP, >> temp_setup.sql
echo     updated_at TIMESTAMP DEFAULT CURRENT_TIMESTAMP ON UPDATE CURRENT_TIMESTAMP >> temp_setup.sql
echo ); >> temp_setup.sql
echo INSERT IGNORE INTO users (username, email, password) VALUES >> temp_setup.sql
echo ('admin', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'), >> temp_setup.sql
echo ('testuser', '<EMAIL>', '$2y$10$92IXUNpkjO0rOQ5byMi.Ye4oKoEa3Ro9llC/.og/at2.uheWG/igi'); >> temp_setup.sql

REM Execute SQL commands
"%MYSQL_BIN%" -u root < temp_setup.sql
if errorlevel 1 (
    echo.
    echo ERROR: Database setup failed!
    echo This might be because:
    echo 1. MySQL root user has a password set
    echo 2. MySQL is not properly configured
    echo.
    echo Please run the following command manually in MySQL:
    echo SOURCE %WEB_DIR%\setup.sql;
    echo.
) else (
    echo Database setup complete.
)

REM Clean up temporary file
del temp_setup.sql >nul 2>&1

echo.
echo ===============================================================================
echo                              SETUP COMPLETE!
echo ===============================================================================
echo.
echo Access Information:
echo   URL: http://localhost/sql_injection_demo/
echo   Database: %DB_NAME%
echo   Web Directory: %WEB_DIR%
echo.
echo Test Credentials:
echo   Email: <EMAIL>
echo   Password: password
echo.
echo Security Reminders:
echo   • This application is intentionally vulnerable
echo   • Use only in isolated environments
echo   • Never expose to the internet
echo   • For educational purposes only
echo.
echo Testing the Vulnerability:
echo   1. Register with username: hacker'); INSERT INTO users (username, email, password) VALUES ('injected', '<EMAIL>', 'password'); --
echo   2. Login with the registered account
echo   3. Update profile to trigger the injection
echo.
echo Troubleshooting:
echo   • Make sure XAMPP Control Panel shows Apache and MySQL as running
echo   • If database setup failed, run setup.sql manually in phpMyAdmin
echo   • Check %WEB_DIR%\db_connect.php for database connection settings
echo.

REM Ask to open browser
set /p open_browser="Open browser now? (y/N): "
if /i "%open_browser%"=="y" (
    start http://localhost/sql_injection_demo/
)

echo.
echo Press any key to exit...
pause >nul
